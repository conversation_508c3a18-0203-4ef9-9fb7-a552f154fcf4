package config

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/yaml.v2"
)

var (
	configMap map[string]any
)

func Init(path ...string) error {
	filePath := "./config/config.yaml"
	if len(path) != 0 {
		filePath = path[0]
	}

	// 读取yaml配置文件
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("read config file, %v", err)
	}

	// 反序列化
	configMap = make(map[string]any)
	err = yaml.Unmarshal(content, &configMap)
	if err != nil {
		return fmt.Errorf("config unmarshal error, %v", err)
	}

	return nil
}

// Get 获取配置项
func Get(key string, out any) error {
	keys := strings.Split(key, ".")
	var current any = configMap

	for _, k := range keys {
		if m, ok := current.(map[string]any); ok {
			if val, exists := m[k]; exists {
				current = val
			} else {
				return fmt.Errorf("config key %s not found", key)
			}
		} else {
			return fmt.Errorf("config key %s not found", key)
		}
	}

	// 将配置值序列化为YAML，然后反序列化到目标结构
	yamlData, err := yaml.Marshal(current)
	if err != nil {
		return fmt.Errorf("marshal config error, %v", err)
	}

	err = yaml.Unmarshal(yamlData, out)
	if err != nil {
		return fmt.Errorf("unmarshal config error, %v", err)
	}

	return nil
}
