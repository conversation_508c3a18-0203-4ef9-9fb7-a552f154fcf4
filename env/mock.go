package env

import (
	"log"
	"testing"

	"github.com/gin-gonic/gin"

	"dt-common/logger"
	"stabilityDigitalBase/config"
	"stabilityDigitalBase/library/mysql"
)

// 单元测试Mock初始化
func Mock(t *testing.T, configPath ...string) {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("failed to init config, error=(%v)", err)
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("failed to init logger, error=(%v)", err)
	}
	logger.Init(&logConfig)

	// MOCK DB
	mysql.MockInit(t)

	gin.SetMode(gin.TestMode)
}
