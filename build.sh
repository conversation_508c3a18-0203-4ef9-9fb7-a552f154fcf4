#!/bin/bash

# 环境
conf=$1
if [ -z "$conf" ]; then
    echo "请指定配置文件后重试"
    exit 1
fi
# copy config.yaml
echo "Config File: "$conf

# 定义路径
HOMEDIR=$(pwd)
OUTDIR=$HOMEDIR/output
echo "初始化项目目录变量"
echo "HOMEDIR: "$HOMEDIR
echo "OUTDIR: "$OUTDIR

# 设置环境变量
export GOROOT=/home/<USER>/soft/go_1.21.3
export PATH=${GOROOT}/bin:${PATH}
echo "设置环境变量"
echo "GOROOT: "$GOROOT
echo "PATH: "$PATH

# 设置git， 保证github mirror能够下载
git config --global http.sslVerify false
go env -w GO111MODULE="on"
go env -w GOPROXY=http://goproxy.duxiaoman-int.com/nexus/repository/go-public/
# GOPRIVATE会等价于GONOPROXY、GONOSUMDB两个变量，所以配置GOPRIVATE之后可以不用配置GONOPROXY、GONOSUMDB
go env -w GOPRIVATE="*.duxiaoman-int.com"
go env -w GONOPROXY="**.duxiaoman-int.com**"
go env -w GONOSUMDB="*"
go env -w "GOFLAGS"="-mod=mod"

# copy files
mkdir -p $OUTDIR
mkdir -p $OUTDIR/log
mkdir -p $OUTDIR/config
mkdir -p $OUTDIR/cmd
mkdir -p $OUTDIR/var

cp config/$conf $OUTDIR/config/config.yaml
cp control.sh $OUTDIR/control.sh

# 生成ent代码
go run -mod=mod entgo.io/ent/cmd/ent generate --feature sql/modifier,sql/execquery,sql/lock,sql/upsert ./library/ent/schema

# 构建
echo "start build"
cd $HOMEDIR/cmd/app
go build -o digital-base
mv digital-base $OUTDIR/cmd/digital-base
echo "done"
