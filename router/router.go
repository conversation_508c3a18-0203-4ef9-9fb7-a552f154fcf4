package router

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/errs"
)

// 健康检查
func HealthCheck(c *gin.Context) {
	gintool.JSON(c, "ok", nil)
}

// 404处理
func HandlerNotFound(c *gin.Context) {
	gintool.JSON(c, nil, errs.CodeNotFound.Detail())
}

// 初始化路由
func Init(mode string, token string) *gin.Engine {
	gin.SetMode(mode)

	// init engine
	r := gin.New()
	r.Use(cors.Default())
	r.Use(gintool.Logger)
	r.Use(gintool.Recovery)

	// 404
	r.NoMethod(HandlerNotFound)
	r.NoRoute(HandlerNotFound)
	// HealthCheck
	r.GET("/healthCheck", HealthCheck)

	Routes(r, token)

	return r
}
