package mysql

import (
	"context"
	"log"
	"testing"
	"time"

	_ "github.com/mattn/go-sqlite3"

	"stabilityDigitalBase/library/ent"
	"stabilityDigitalBase/library/ent/enttest"
)

// 初始化数据库连接
func MockInit(t *testing.T) (*ent.Client, error) {
	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&_fk=1&cache=shared")
	log.Println("mysql connect successfully")

	// 创建表结构
	err := client.Schema.Create(context.Background())
	if err != nil {
		log.Fatalf("failed creating schema resources: %v", err)
	}

	// save client
	clients = make(map[string]*Client)
	clients["default"] = &Client{client}

	timeout = 30 * time.Second
	mode = ModeMock

	return client, nil
}
