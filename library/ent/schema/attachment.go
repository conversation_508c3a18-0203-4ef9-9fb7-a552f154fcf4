package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Attachment holds the schema definition for the Attachment entity.
type Attachment struct {
	ent.Schema
}

// Fields of the Attachment.
func (Attachment) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		field.Int64("incident_id").StructTag(`json:"incidentId"`).Comment("故障ID"),

		field.Enum("type").Values("image", "document", "text").Comment("附件类型：image-图片，document-文档，text-文本"),
		field.String("file_name").StructTag(`json:"fileName"`).Optional().Comment("文件名"),
		field.String("file_url").StructTag(`json:"fileUrl"`).Optional().Comment("文件URL地址"),
		field.String("file_size").StructTag(`json:"fileSize"`).Optional().Comment("文件大小"),
		field.String("mime_type").StructTag(`json:"mimeType"`).Optional().Comment("文件MIME类型"),
		field.Text("content").Optional().Comment("文本内容"),
		field.String("uploader").NotEmpty().Comment("上传人"),

		field.Enum("status").Values("normal", "deleted").Default("normal").Comment("状态: normal-正常，deleted-已删除"),
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).Default(time.Now).UpdateDefault(time.Now).Comment("更新时间"),
	}
}

// Edges of the Attachment.
func (Attachment) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("incident", Incident.Type).
			Ref("attachments").
			Field("incident_id").
			Unique().
			Required(),
	}
}

// Annotations of the Attachment.
func (Attachment) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "attachment"},
	}
}
