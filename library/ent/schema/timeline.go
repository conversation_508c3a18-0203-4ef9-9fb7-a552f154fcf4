package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Timeline holds the schema definition for the Timeline entity.
type Timeline struct {
	ent.Schema
}

// Fields of the Timeline.
func (Timeline) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		field.Int64("incident_id").StructTag(`json:"incidentId"`).Comment("故障ID"),

		field.Time("start_time").StructTag(`json:"startTime"`).Comment("开始时间戳"),
		field.Time("end_time").StructTag(`json:"endTime"`).Optional().Comment("结束时间戳"),
		field.Enum("type").Values("occurred", "discovered", "located", "operated", "recovered").Optional().Comment("事件类型：occurred-故障发生，discovered-故障发现, located-故障定位，operated-止损操作，recovered-故障恢复"),
		field.Text("description").Comment("事件描述"),

		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).Default(time.Now).UpdateDefault(time.Now).Comment("更新时间"),
	}
}

// Edges of the Timeline.
func (Timeline) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("incident", Incident.Type).
			Ref("timelines").
			Field("incident_id").
			Unique().
			Required(),
	}
}

// Annotations of the Timeline.
func (Timeline) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "timeline"},
	}
}
