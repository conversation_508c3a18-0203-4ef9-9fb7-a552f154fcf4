package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Problem holds the schema definition for the Problem entity.
type Problem struct {
	ent.Schema
}

// Fields of the Problem.
func (Problem) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		field.Int64("incident_id").StructTag(`json:"incidentId"`).Comment("故障ID"),

		field.String("phase").Default("").Comment("环节"),
		field.Text("content").Optional().Comment("暴露的问题"),

		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).Default(time.Now).UpdateDefault(time.Now).Comment("更新时间"),
	}
}

// Edges of the Problem.
func (Problem) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("incident", Incident.Type).Ref("problems").Field("incident_id").Unique().Required(),
		edge.To("improvements", Improvement.Type),
	}
}

// Indexes of the Problem.
func (Problem) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("incident_id", "phase"),
	}
}

// Annotations of the Problem.
func (Problem) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "problem"},
	}
}
