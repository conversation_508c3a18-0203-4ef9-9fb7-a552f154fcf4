package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Improvement holds the schema definition for the Improvement entity.
type Improvement struct {
	ent.Schema
}

// Fields of the Improvement.
func (Improvement) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		field.Int64("incident_id").StructTag(`json:"incidentId"`).Comment("故障ID"),
		field.Int64("problem_id").StructTag(`json:"problemId"`).Comment("问题ID"),

		field.Text("content").NotEmpty().Comment("改进措施内容说明"),
		field.String("owner").NotEmpty().Comment("整改负责人"),
		field.Time("expected_time").StructTag(`json:"expectedTime"`).Optional().Comment("预期完成时间"),

		field.Enum("status").Values("pending", "in_progress", "completed").Default("pending").Comment("完成状态：pending-待处理，in_progress-进行中，completed-已完成"),
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).Default(time.Now).UpdateDefault(time.Now).Comment("更新时间"),
	}
}

// Edges of the Improvement.
func (Improvement) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("incident", Incident.Type).Ref("improvements").Field("incident_id").Unique().Required(),
		edge.From("problem", Problem.Type).Ref("improvements").Field("problem_id").Unique().Required(),
	}
}

// Indexes of the Improvement.
func (Improvement) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("problem_id", "status"),
		index.Fields("owner", "status"),
		index.Fields("expected_time"),
	}
}

// Annotations of the Improvement.
func (Improvement) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "improvement"},
	}
}
