package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Incident holds the schema definition for the Incident entity.
type Incident struct {
	ent.Schema
}

// Fields of the Incident.
func (Incident) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Unique().Comment("主键ID"),

		// 一句话故障管理（必填）
		field.Time("happened_at").StructTag(`json:"happendedAt"`).Comment("发生时间"),
		field.String("summary").NotEmpty().Comment("故障简述"),
		field.String("direct_cause").StructTag(`json:"directCause"`).NotEmpty().Comment("直接原因"),

		// 一句话故障管理（选填）
		field.Int("time_discovered").StructTag(`json:"timeDiscovered"`).Optional().Comment("发现时长"),
		field.Int("time_located").StructTag(`json:"timeLocated"`).Optional().Comment("定位时长"),
		field.Int("time_recovered").StructTag(`json:"timeRecovered"`).Optional().Comment("恢复时长"),
		field.Bool("auto_stop_loss").StructTag(`json:"autoStopLoss"`).Default(false).Comment("是否具备自动止损能力"),
		field.Bool("auto_stop_loss_effectived").StructTag(`json:"autoStopLossEffectived"`).Optional().Comment("自动止损是否生效"),

		// 复盘必填
		field.String("root_cause").StructTag(`json:"rootCause"`).Optional().Comment("根本原因"),
		field.Text("impact").Optional().Comment("故障影响：造成的损失"),
		field.Enum("level").Values("P0", "P1", "P2", "P3", "NONE").Comment("故障级别：P0/P1/P2/P3/NONE(无损)"),
		field.String("owner").NotEmpty().Comment("故障负责人"),

		// 高级属性
		field.Enum("nature").Values("good", "bad").Comment("故障性质：good-好案例，bad-坏案例"),
		field.JSON("tag", []string{}).Optional().Comment("标签"),

		// 状态信息
		field.Enum("status").Values("normal", "review", "closed", "deleted").Default("normal").Comment("状态：normal-正常，review-复盘，closed-复盘完成已关闭, deleted-已删除"),
		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).Default(time.Now).UpdateDefault(time.Now).Comment("更新时间"),
		field.Time("deleted_at").StructTag(`json:"deletedAt"`).Optional().Comment("删除时间"),
	}
}

// Edges of the Incident.
func (Incident) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("timelines", Timeline.Type),
		edge.To("problems", Problem.Type),
		edge.To("improvements", Improvement.Type),
		edge.To("attachments", Attachment.Type),
	}
}

// Indexes of the Incident.
func (Incident) Indexes() []ent.Index {
	return []ent.Index{
		// 查询优化索引
		index.Fields("status", "created_at"), // 列表查询按创建时间排序
		index.Fields("status", "level"),      // 按故障级别查询
		index.Fields("status", "owner"),      // 按负责人查询
		index.Fields("status", "nature"),     // 按性质查询
	}
}

// Annotations of the Incident.
func (Incident) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "incident"},
	}
}
