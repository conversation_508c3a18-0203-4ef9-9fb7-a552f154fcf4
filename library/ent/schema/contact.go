package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Contact holds the schema definition for the Contact entity.
type Contact struct {
	ent.Schema
}

// Fields of the Contact.
func (Contact) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id").Unique().Comment("主键ID"),

		field.String("team").NotEmpty().Comment("团队"),
		field.String("business").NotEmpty().Comment("业务"),
		field.String("account").NotEmpty().Comment("账号"),
		field.String("name").NotEmpty().Comment("姓名"),

		field.Time("start_time").Comment("上任时间"),
		field.Time("end_time").Optional().Comment("卸任时间"),

		field.Time("created_at").StructTag(`json:"createdAt"`).Default(time.Now).Comment("创建时间"),
		field.Time("updated_at").StructTag(`json:"updatedAt"`).Default(time.Now).UpdateDefault(time.Now).Comment("更新时间"),
	}
}

// Edges of the Contact.
func (Contact) Edges() []ent.Edge {
	return []ent.Edge{}
}

// Indexes of the Contact.
func (Contact) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("team"),
		index.Fields("business"),
		index.Fields("account"),
	}
}

// Annotations of the Contact.
func (Contact) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "contact"},
	}
}
