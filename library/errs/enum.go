package errs

import (
	"fmt"
	"net/http"
)

type Code string

// 获取错误信息
func (e Code) Error() string {
	return eMap[e].Message
}

// 获取http status
func (e Code) Status() int {
	return eMap[e].Status
}

// 使用全新的Message，如果希望保留原始Message，请使用.Append()方法
func (e Code) Detail(msg ...string) *HttpError {
	return New(e, msg...)
}

// 在原始Message的基础上追加Message，如果不想要原始Message，请使用.Detail()方法
func (e Code) Append(msg ...string) *HttpError {
	if v, exist := eMap[e]; exist {
		msg = append([]string{v.Message}, msg...)
	}
	return New(e, msg...)
}

func (e Code) Int() int {
	return eMap[e].IV
}

const Success Code = "00000"

// 错误来自后端代码
const (
	CodeUnknown         Code = "S0001" // 未知错误
	CodeVoidReturn      Code = "S0002" // 空返回值
	CodeMissingConfig   Code = "S0003" // 找不到配置
	CodeMarshalError    Code = "S0004" // 序列化失败
	CodeUnmarshalError  Code = "S0005" // 反序列化失败
	CodeShellExecFailed Code = "S0006" // shell执行失败
	CodeFileNotExist    Code = "S0007" // 文件不存在
	CodeFolderNotExist  Code = "S0008" // 目录不存在
	CodeReadFile        Code = "S0009" // 文件读取失败
	CodeWriteFile       Code = "S0010" // 文件写入失败
)

// 错误来源于用户
const (
	CodeNotFound         Code = "U0001" // 不存在的接口
	CodeRequestParameter Code = "U0002" // 请求参数错误
	CodeMissingToken     Code = "U1003" // 未携带token
	CodePermissionDenied Code = "U1004" // 无权限访问
	CodeTokenOutOfDate   Code = "U1005" // token过期

	// Project相关错误码
	CodeProjectNotFound     Code = "U2001" // 项目不存在
	CodeProjectNameExists   Code = "U2002" // 项目名称已存在
	CodeProjectCreateFailed Code = "U2003" // 项目创建失败
	CodeProjectUpdateFailed Code = "U2004" // 项目更新失败
	CodeProjectDeleteFailed Code = "U2005" // 项目删除失败

	// User相关错误码
	CodeUserNotFound     Code = "U2101" // 用户不存在
	CodeUserNameExists   Code = "U2102" // 用户名称已存在
	CodeUserCreateFailed Code = "U2103" // 用户创建失败
	CodeUserUpdateFailed Code = "U2104" // 用户更新失败
	CodeUserDeleteFailed Code = "U2105" // 用户删除失败

	// Department相关错误码
	CodeDepartmentNotFound     Code = "U2201" // 部门不存在
	CodeDepartmentNameExists   Code = "U2202" // 部门名称已存在
	CodeDepartmentCreateFailed Code = "U2203" // 部门创建失败
	CodeDepartmentUpdateFailed Code = "U2204" // 部门更新失败
	CodeDepartmentDeleteFailed Code = "U2205" // 部门删除失败
)

// 错误来自外部系统（数据库、用户中心、成本中心...）
const (
	CodeRequestFailed           Code = "E0001" // 外部请求失败
	CodeDatabase                Code = "E0002" // 数据库相关错误
	CodeConnectionFail          Code = "E0003" // 连接失败
	CodeConnectionLost          Code = "E0004" // 连接关闭
	CodeAuthCenterRequestFailed Code = "E1001" // 认证中心请求失败
	CodeAuthenticationFailed    Code = "E1002" // 认证失败
)

const (
	CodeUndefinedValue Code = "S1003"
	CodeStageParameter Code = "S1004"

	CodeInvalidParameter Code = "S1005" // 参数错误
	CodeInternalError    Code = "S1006" // 内部错误
)

// 错误码与http status映射，不在map中的一律按 500 处理
var eMap = map[Code]HttpError{
	Success: {Status: http.StatusOK, Message: "success", IV: 0},

	CodeUnknown:         {Status: http.StatusInternalServerError, Message: "unknown error, please contact op", IV: 10001},
	CodeVoidReturn:      {Status: http.StatusInternalServerError, Message: "no return value", IV: 10002},
	CodeMissingConfig:   {Status: http.StatusInternalServerError, Message: "can't find config", IV: 10003},
	CodeMarshalError:    {Status: http.StatusInternalServerError, Message: "marshal error", IV: 10004},
	CodeUnmarshalError:  {Status: http.StatusInternalServerError, Message: "unmarshal error", IV: 10005},
	CodeShellExecFailed: {Status: http.StatusInternalServerError, Message: "shell exec failed", IV: 10006},
	CodeFileNotExist:    {Status: http.StatusInternalServerError, Message: "file not exist", IV: 10007},
	CodeFolderNotExist:  {Status: http.StatusInternalServerError, Message: "folder not exist", IV: 10008},
	CodeReadFile:        {Status: http.StatusInternalServerError, Message: "read file error", IV: 10009},
	CodeWriteFile:       {Status: http.StatusInternalServerError, Message: "write file error", IV: 10010},

	CodeNotFound:         {Status: http.StatusNotFound, Message: "can not found api", IV: 20001},
	CodeRequestParameter: {Status: http.StatusBadRequest, Message: "parameter error", IV: 20002},
	CodeMissingToken:     {Status: http.StatusNonAuthoritativeInfo, Message: "missing token", IV: 21001},
	CodePermissionDenied: {Status: http.StatusNotFound, Message: "permission denied", IV: 21002},
	CodeTokenOutOfDate:   {Status: http.StatusNotFound, Message: "token out of date", IV: 21003},

	// Project相关错误码映射
	CodeProjectNotFound:     {Status: http.StatusNotFound, Message: "项目不存在", IV: 22001},
	CodeProjectNameExists:   {Status: http.StatusBadRequest, Message: "项目名称已存在", IV: 22002},
	CodeProjectCreateFailed: {Status: http.StatusInternalServerError, Message: "项目创建失败", IV: 22003},
	CodeProjectUpdateFailed: {Status: http.StatusInternalServerError, Message: "项目更新失败", IV: 22004},
	CodeProjectDeleteFailed: {Status: http.StatusInternalServerError, Message: "项目删除失败", IV: 22005},

	// User相关错误码映射
	CodeUserNotFound:     {Status: http.StatusNotFound, Message: "用户不存在", IV: 22101},
	CodeUserNameExists:   {Status: http.StatusBadRequest, Message: "用户名称已存在", IV: 22102},
	CodeUserCreateFailed: {Status: http.StatusInternalServerError, Message: "用户创建失败", IV: 22103},
	CodeUserUpdateFailed: {Status: http.StatusInternalServerError, Message: "用户更新失败", IV: 22104},
	CodeUserDeleteFailed: {Status: http.StatusInternalServerError, Message: "用户删除失败", IV: 22105},

	// Department相关错误码映射
	CodeDepartmentNotFound:     {Status: http.StatusNotFound, Message: "部门不存在", IV: 22201},
	CodeDepartmentNameExists:   {Status: http.StatusBadRequest, Message: "部门名称已存在", IV: 22202},
	CodeDepartmentCreateFailed: {Status: http.StatusInternalServerError, Message: "部门创建失败", IV: 22203},
	CodeDepartmentUpdateFailed: {Status: http.StatusInternalServerError, Message: "部门更新失败", IV: 22204},
	CodeDepartmentDeleteFailed: {Status: http.StatusInternalServerError, Message: "部门删除失败", IV: 22205},

	CodeRequestFailed:           {Status: http.StatusInternalServerError, Message: "请求失败", IV: 30000},
	CodeDatabase:                {Status: http.StatusInternalServerError, Message: "数据库错误", IV: 30001},
	CodeConnectionFail:          {Status: http.StatusInternalServerError, Message: "数据库连接失败", IV: 30002},
	CodeConnectionLost:          {Status: http.StatusInternalServerError, Message: "数据库连接丢失", IV: 30003},
	CodeAuthCenterRequestFailed: {Status: http.StatusInternalServerError, Message: "认证中心请求失败", IV: 31001},
	CodeAuthenticationFailed:    {Status: http.StatusInternalServerError, Message: "认证失败", IV: 31002},
}

// 注册错误码
// 错误码格式可以自定义
// 错误类型：U - 来自用户(2)，S - 来自后端代码(1)，E - 来自外部系统(3)
func Register(code Code, msg string, status int, iv int) error {
	if _, exist := eMap[code]; exist {
		return fmt.Errorf("error code %s already exist", code)
	}

	eMap[code] = HttpError{
		Code:    code,
		Message: msg,
		Status:  status,
		IV:      iv,
	}
	return nil
}
