package main

import (
	"fmt"
	"runtime"

	"dt-common/logger"
	"stabilityDigitalBase/env"
	"stabilityDigitalBase/router"
)

func main() {
	// 控制main使用cpu的总数,只用一颗cpu
	runtime.GOMAXPROCS(1)

	// 初始化环境变量
	cfg := env.Init()
	logger.Info("environment initialization successfully")

	// 初始化定时器配置
	// var timerCfg timer.Config
	// err := config.Get("timer", &timerCfg)
	// if err != nil {
	// 	log.Panicf("failed to init timer, error=(%v)", err)
	// }

	// // 启动定时器
	// err = timer.Start(&timerCfg)
	// if err != nil {
	// 	timer.Stop()
	// 	log.Panicf("failed to start timer, error=(%v)", err)
	// }
	// logger.Info("timer started successfully, service starting...")

	// 启动服务
	app := router.Init(cfg.Mode, cfg.Token)
	app.Run(fmt.Sprintf(":%d", cfg.Port))
}
