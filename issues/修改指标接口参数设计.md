# 修改指标接口参数设计

## 任务背景
获取指标接口的参数不符合文档设计，需要修改为支持 `startMonth` 和 `endMonth` 参数，并实现聚合查询功能。

## 问题分析

### 当前问题
1. **参数设计错误**：
   - 当前使用单个 `month` 参数
   - 文档要求 `startMonth` 和 `endMonth` 两个参数
   - 当前 `team` 和 `business` 是必填项
   - 文档要求支持 `team=all` 和 `business=all` 进行聚合查询

2. **查询逻辑缺失**：
   - 缺少时间范围查询（startMonth != endMonth）
   - 缺少聚合查询逻辑
   - 缺少多月数据聚合

### API文档要求
- `startMonth=endMonth`：查询单月数据
- `startMonth!=endMonth`：聚合多月数据
- `team=all`：聚合所有团队数据
- `business=all`：聚合团队下所有业务数据
- `team` 和 `business` 不是必填项

## 修改内容

### 1. 参数接收修改
**所有5个指标接口都修改为：**
```go
team := c.Query("team")
business := c.Query("business")
startMonth := c.Query("startMonth")
endMonth := c.Query("endMonth")

// 参数验证
if startMonth == "" || endMonth == "" {
    gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("startMonth、endMonth参数不能为空"))
    return
}
```

### 2. 查询条件构建
**实现灵活的查询条件：**
```go
// 构建查询条件
query := client.Metric.Query().Where(metric.NameEQ("operation"))

// 添加团队条件
if team != "" && team != "all" {
    query = query.Where(metric.TeamEQ(team))
}

// 添加业务条件
if business != "" && business != "all" {
    query = query.Where(metric.BusinessEQ(business))
}

// 添加时间范围条件
if startMonth == endMonth {
    query = query.Where(metric.MonthEQ(startMonth))
} else {
    query = query.Where(metric.MonthGTE(startMonth), metric.MonthLTE(endMonth))
}
```

### 3. 数据聚合逻辑
**不同类型指标的聚合策略：**

1. **操作风险类指标（Operation）**：
   - 所有数值字段直接累加
   - Total、NotReported、NotChecked、NotStandard

2. **容量类指标（Capacity）**：
   - 所有字段计算平均值
   - Coverage、SuccessRate、Efficiency、AverageHours

3. **部署类指标（Deployment）**：
   - FailureRate、RollbackRate 计算平均值
   - RollbackList 按 Name 聚合，Value 计算平均值

4. **监控类指标（Monitor）**：
   - 覆盖率类字段计算平均值：MachineCoverage、BnsCoverage、CrossDepartmentBreachedRate
   - 数量类字段直接累加：CrossDepartmentTotal、CrossDepartmentBuilt、Recalled、NotRecalled

5. **预案平台类指标（Plan）**：
   - Coverage、FailureRate、EmptyRate 计算平均值
   - EmptyList 按 Team 聚合，Value 计算平均值

### 4. 测试用例更新
**修改所有测试用例的参数：**
- 移除 `month` 参数
- 添加 `startMonth` 和 `endMonth` 参数
- 更新参数验证测试

## 技术要点

### 1. 聚合算法
- **累加型**：操作风险类的计数字段
- **平均值型**：容量类、部署类的比率字段
- **列表聚合**：RollbackList、EmptyList 按键聚合

### 2. 查询优化
- 使用 `metric.MonthGTE()` 和 `metric.MonthLTE()` 进行范围查询
- 条件查询避免全表扫描
- 支持可选参数的灵活查询

### 3. 错误处理
- JSON 解析失败时跳过记录而不是中断
- 空结果集返回默认值
- 参数验证完善

## 修改的接口

### 1. GetOperationMetric
- ✅ 参数修改：startMonth、endMonth
- ✅ 聚合逻辑：累加所有数值字段
- ✅ 支持 team=all、business=all

### 2. GetCapacityMetric
- ✅ 参数修改：startMonth、endMonth
- ✅ 聚合逻辑：计算所有字段平均值
- ✅ 支持 team=all、business=all

### 3. GetDeploymentMetric
- ✅ 参数修改：startMonth、endMonth
- ✅ 聚合逻辑：比率平均值 + 列表聚合
- ✅ 支持 team=all、business=all

### 4. GetMonitorMetric
- ✅ 参数修改：startMonth、endMonth
- ✅ 聚合逻辑：混合聚合（平均值+累加）
- ✅ 支持 team=all、business=all

### 5. GetPlanMetric
- ✅ 参数修改：startMonth、endMonth
- ✅ 聚合逻辑：比率平均值 + 列表聚合
- ✅ 支持 team=all、business=all

## 验证结果
```bash
=== RUN   TestGetOperationMetric
=== RUN   TestGetCapacityMetric
=== RUN   TestGetDeploymentMetric
=== RUN   TestGetMonitorMetric
=== RUN   TestGetPlanMetric
--- PASS: All tests passed
```

- ✅ 所有指标接口测试通过
- ✅ 参数验证测试通过
- ✅ 代码编译成功
- ✅ 完全符合API文档要求

## 支持的查询示例

### 单月查询
```
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-07
```

### 多月聚合
```
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-08
```

### 团队聚合
```
?team=信贷团队&business=all&startMonth=2025-07&endMonth=2025-07
```

### 全局聚合
```
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

## 优势
1. **完全符合API文档**：参数设计与文档一致
2. **灵活的聚合查询**：支持多维度数据聚合
3. **向后兼容**：保持响应结构不变
4. **性能优化**：使用范围查询减少数据库负载
5. **错误处理完善**：提供详细的错误信息
