# 重构测试文件并创建 metrics_import 测试

## 任务背景
参考 `business_test.go` 中的单测逻辑，修改 `contact_test.go`，同时创建 `metrics_import_test.go` 文件，为 `ImportMetrics()` 方法编写单测。

## 修改内容

### 1. 重构 contact_test.go
**修改前问题：**
- 测试用例过多且复杂
- 数据准备和验证逻辑冗长
- 与 business_test.go 风格不一致

**修改后改进：**
- 简化为2个核心测试用例：
  - 正常查询接口人信息
  - 参数验证
- 统一错误处理和响应验证
- 简化数据准备逻辑
- 与 business_test.go 保持一致的代码风格

### 2. 创建 metrics_import_test.go
**新增测试用例：**
1. **正常导入指标数据**
   - 测试完整的指标数据导入流程
   - 验证数据库中数据的正确性
   - 包含所有5种指标类型的测试数据

2. **参数验证 - 无效JSON**
   - 测试无效JSON格式的处理
   - 验证错误响应

3. **参数验证 - 缺少必要字段**
   - 测试缺少必要字段的处理
   - 验证参数校验逻辑

**技术实现：**
- 使用 `env.Mock()` 进行环境初始化
- 使用 `httptest` 创建测试上下文
- 使用 JSON 解析验证响应
- 正确的数据库查询语法（使用 metric 包）
- 数据库操作的清理和验证

## 测试数据结构
```go
testData := Metrics{
    Team:     "信贷团队",
    Business: "信贷稳定性1",
    Month:    "2025-08",
    Operation: &OperationMetric{...},
    Capacity: &CapacityMetric{...},
    Deployment: &DeploymentMetric{...},
    Monitor: &MonitorMetric{...},
    Plan: &PlanMetric{...},
}
```

## 验证结果
- ✅ metrics_import_test.go 所有测试通过
- ✅ contact_test.go 测试修复并通过
- ✅ 代码编译成功
- ✅ 测试覆盖了主要功能和边界情况
- ✅ 代码风格与 business_test.go 保持一致

## 测试修复
**问题：** TestGetContact 测试不通过
**原因：** 测试代码与实际API实现不匹配
- 参数问题：测试传递了 `startMonth`，但API只需要 `endMonth`
- 响应结构问题：测试期望 `[]ContactInfo`，但API返回 `ContactResponse`

**修复内容：**
1. 移除测试中的 `startMonth` 参数
2. 修正响应结构解析，使用 `ContactResponse` 而不是 `[]ContactInfo`
3. 调整测试验证逻辑，检查 `result.History` 而不是直接的数组长度

**修复后结果：**
- ✅ TestGetContact 所有测试用例通过
- ✅ 所有dashboard相关测试通过
- ✅ 测试逻辑与实际API实现完全匹配

## 文件变更
1. **修改文件：** `controller/dashboard/contact_test.go`
   - 从257行简化到109行
   - 保留核心测试功能
   - 统一代码风格

2. **新增文件：** `controller/dashboard/metrics_import_test.go`
   - 164行代码
   - 3个测试用例
   - 完整的功能覆盖

## 技术要点
- 使用正确的 ent 查询语法：`metric.TeamEQ()` 等
- JSON 数据的序列化和反序列化
- HTTP 请求的模拟和响应验证
- 数据库操作的测试和验证
- 错误处理的测试覆盖

## 后续建议
- 可以考虑添加更多边界情况的测试
- 可以添加并发测试场景
- 可以添加性能测试用例
