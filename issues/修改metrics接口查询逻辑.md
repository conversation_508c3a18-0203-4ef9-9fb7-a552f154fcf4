# 修改 metrics.go 中5个接口的查询逻辑

## 任务背景
根据 `metrics_import.go` 中对 metric 表的使用方式，修改 `metrics.go` 下5个接口的查询逻辑。

## 问题分析
1. `metrics_import.go` 中，数据是按照指标类型（operation、capacity、deployment、monitor、plan）存储的，每个类型的完整JSON数据存储在一条记录的 `value` 字段中
2. `metrics.go` 中的5个接口错误地将这些指标类型当作单独的字段名来查询，而不是查询对应的指标类型记录并解析其JSON值

## 解决方案
采用方案1：修改查询逻辑，正确解析JSON数据
- 查询对应的指标类型记录（name="operation"等）
- 解析 value 字段中的JSON数据
- 构建返回结果

## 修改内容

### 1. 添加 JSON 包导入
```go
import (
    "context"
    "encoding/json"  // 新增
    "strconv"
    "time"
    // ...
)
```

### 2. 修改 GetOperationMetric 接口
- 查询 name="operation" 的记录
- 使用 `json.Unmarshal` 解析 JSON 数据

### 3. 修改 GetCapacityMetric 接口
- 查询 name="capacity" 的记录
- 使用 `json.Unmarshal` 解析 JSON 数据

### 4. 修改 GetDeploymentMetric 接口
- 查询 name="deployment" 的记录
- 使用 `json.Unmarshal` 解析 JSON 数据
- 正确处理 RollbackList 的 JSON 解析

### 5. 修改 GetMonitorMetric 接口
- 查询 name="monitor" 的记录
- 使用 `json.Unmarshal` 解析 JSON 数据
- 正确处理 NotRecalled 嵌套结构的解析

### 6. 修改 GetPlanMetric 接口
- 查询 name="plan" 的记录
- 使用 `json.Unmarshal` 解析 JSON 数据
- 正确处理 EmptyList 的 JSON 解析

## 技术要点
- 使用 `json.Unmarshal` 解析 JSON 数据
- 添加适当的错误处理
- 保持现有的参数验证和错误返回逻辑
- 保留 `safeParseInt` 和 `safeParseFloat` 函数（测试文件需要使用）

## 预期结果
- 5个接口能够正确查询和返回对应的指标数据
- 保持与现有 API 接口的兼容性
- 错误处理完善，数据解析安全

## 修改状态
✅ 已完成所有5个接口的修改
✅ 保留了测试文件需要的辅助函数
✅ 添加了适当的错误处理
