# 修改 GetCapacityMetric() 和 GetPlanMetric() 函数

## 任务背景

### GetCapacityMetric() 函数修改要求：
1. 增加 startMonth 和 endMonth 的结构校验，必须是 YYYY-MM 格式
2. startMonth 和 endMonth 转换成 YYYY-MM-01 00:00:00 的 time.Time 类型
3. 在 SQL 查询中增加 >= startMonth 和 <= endMonth 条件
4. 在 SQL 查询中增加 teamEQ 和 businessEQ 条件，当参数 = all 时，去掉对应参数的查询条件
5. 当出现多条查询记录时，聚合规则为：求平均

### GetPlanMetric() 函数修改要求：
1. 增加 startMonth 和 endMonth 的结构校验，必须是 YYYY-MM 格式
2. startMonth 和 endMonth 转换成 YYYY-MM-01 00:00:00 的 time.Time 类型
3. 在 SQL 查询中增加 >= startMonth 和 <= endMonth 条件
4. 在 SQL 查询中增加 teamEQ 和 businessEQ 条件，当参数 = all 时，去掉对应参数的查询条件
5. 当出现多条查询记录时，聚合规则为：
   - coverage、failureRate、emptyRate 求平均
   - emptyList 合并，若 productLine 相同，则 value 求平均，并按 value 倒排

## 修改内容

### 1. 时间格式验证（两个函数相同）
**新增时间格式验证：**
```go
// 验证时间格式（YYYY-MM）
startMatched, _ := regexp.MatchString(`^\d{4}-\d{2}$`, startMonth)
endMatched, _ := regexp.MatchString(`^\d{4}-\d{2}$`, endMonth)
if !startMatched || !endMatched {
    gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("startMonth、endMonth必须是YYYY-MM格式"))
    return
}
```

### 2. 时间类型转换（两个函数相同）
**新增时间转换逻辑（支持时区）：**
```go
// 转换时间格式（YYYY-MM -> YYYY-MM-01 00:00:00 +08:00）
loc, _ := time.LoadLocation("Asia/Shanghai")
startTime, err := time.ParseInLocation("2006-01-02", startMonth+"-01", loc)
if err != nil {
    gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("startMonth时间格式错误"))
    return
}
endTime, err := time.ParseInLocation("2006-01-02", endMonth+"-01", loc)
if err != nil {
    gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("endMonth时间格式错误"))
    return
}
```

### 3. GetCapacityMetric 聚合规则
**聚合逻辑（已正确实现）：**
```go
// 聚合数据（容量类指标需要计算平均值）
for _, record := range metricRecords {
    var metric CapacityMetric
    if err := json.Unmarshal([]byte(record.Value), &metric); err != nil {
        continue // 跳过解析失败的记录
    }
    result.Coverage += metric.Coverage
    result.SuccessRate += metric.SuccessRate
    result.Efficiency += metric.Efficiency
    result.AverageHours += metric.AverageHours
}

// 计算平均值
result.Coverage /= float64(count)
result.SuccessRate /= float64(count)
result.Efficiency /= float64(count)
result.AverageHours /= float64(count)
```

### 4. GetPlanMetric 聚合规则优化
**修改前（错误的聚合方式）：**
```go
emptyMap := make(map[string]float64)
for _, item := range metric.EmptyList {
    emptyMap[item.ProductLine] += item.Value
}
// 构建聚合后的切空列表
for teamName, value := range emptyMap {
    result.EmptyList = append(result.EmptyList, &EmptyItem{
        ProductLine: teamName,
        Value:       value / float64(count), // 错误：按记录数平均
    })
}
```

**修改后（正确的聚合方式）：**
```go
emptyMap := make(map[string]float64) // 用于聚合切空列表
emptyCount := make(map[string]int)   // 用于记录每个productLine出现的次数

for _, record := range metricRecords {
    // 聚合切空列表（按productLine合并，记录出现次数）
    for _, item := range metric.EmptyList {
        emptyMap[item.ProductLine] += item.Value
        emptyCount[item.ProductLine]++
    }
}

// 构建聚合后的切空列表（按productLine求平均）
for productLine, totalValue := range emptyMap {
    avgValue := totalValue / float64(emptyCount[productLine])
    result.EmptyList = append(result.EmptyList, &EmptyItem{
        ProductLine: productLine,
        Value:       avgValue, // 正确：按该productLine出现次数平均
    })
}

// 按 value 倒排（降序排列）
sort.Slice(result.EmptyList, func(i, j int) bool {
    return result.EmptyList[i].Value > result.EmptyList[j].Value
})
```

## 测试用例

### 1. 参数验证测试（两个函数相同）
- **缺少参数测试**：验证 startMonth、endMonth 必填
- **时间格式验证测试**：验证 YYYY-MM 格式要求
- **正常查询测试**：验证正确参数的处理

### 2. GetPlanMetric 排序测试
- **EmptyList 排序测试**：验证按 value 降序排列功能

### 3. 测试结果
```bash
=== RUN   TestGetCapacityMetric
=== RUN   TestGetCapacityMetric/test:_参数验证_-_缺少参数
=== RUN   TestGetCapacityMetric/test:_时间格式验证
=== RUN   TestGetCapacityMetric/test:_正常查询
--- PASS: TestGetCapacityMetric (0.01s)

=== RUN   TestGetPlanMetric
=== RUN   TestGetPlanMetric/test:_参数验证_-_缺少参数
=== RUN   TestGetPlanMetric/test:_时间格式验证
=== RUN   TestGetPlanMetric/test:_正常查询
--- PASS: TestGetPlanMetric (0.00s)

=== RUN   TestPlanMetric_EmptyListSorting
--- PASS: TestPlanMetric_EmptyListSorting (0.00s)
```

## 技术要点

### 1. 时间处理
- **格式验证**：使用正则表达式验证 `^\d{4}-\d{2}$` 格式
- **时间转换**：将 `YYYY-MM` 转换为 `YYYY-MM-01 00:00:00 +08:00`
- **时区支持**：使用 `Asia/Shanghai` 时区，确保时间为 +08:00
- **类型安全**：使用 `time.Time` 类型进行数据库查询

### 2. GetCapacityMetric 聚合算法
- **全部求平均**：所有指标（Coverage、SuccessRate、Efficiency、AverageHours）都求平均
- **简单聚合**：直接累加后除以记录数

### 3. GetPlanMetric 聚合算法
- **基础指标求平均**：coverage、failureRate、emptyRate 求平均
- **emptyList 聚合**：按 productLine 分组，每个 productLine 的 value 按该 productLine 出现次数求平均
- **排序优化**：emptyList 按 value 降序排列，便于前端展示

### 4. 查询优化
- **条件查询**：支持可选的 team 和 business 参数
- **范围查询**：使用 `MonthGTE` 和 `MonthLTE` 进行时间范围查询
- **聚合查询**：支持 `team=all` 和 `business=all` 的聚合查询

## 支持的查询场景

### 1. 单月查询
```
?team=信贷团队&business=信贷稳定性1&startMonth=2025-08&endMonth=2025-08
```

### 2. 多月聚合
```
?team=信贷团队&business=信贷稳定性1&startMonth=2025-07&endMonth=2025-08
```

### 3. 团队聚合
```
?team=信贷团队&business=all&startMonth=2025-08&endMonth=2025-08
```

### 4. 全局聚合
```
?team=all&business=all&startMonth=2025-07&endMonth=2025-08
```

## 修改状态
- ✅ GetCapacityMetric 时间格式验证
- ✅ GetCapacityMetric 时间类型转换
- ✅ GetCapacityMetric 时区支持（+08:00）
- ✅ GetCapacityMetric 聚合规则验证（已正确实现）
- ✅ GetPlanMetric 时间格式验证
- ✅ GetPlanMetric 时间类型转换
- ✅ GetPlanMetric 时区支持（+08:00）
- ✅ GetPlanMetric 聚合规则优化（正确的平均计算）
- ✅ GetPlanMetric emptyList 按 value 倒排
- ✅ 测试用例完善（包括排序测试）
- ✅ 所有测试通过
- ✅ 代码编译成功

## 优势
1. **严格的参数验证**：确保输入数据的正确性
2. **类型安全**：使用 time.Time 类型避免字符串比较的问题
3. **正确的聚合算法**：
   - GetCapacityMetric：全部指标求平均
   - GetPlanMetric：基础指标求平均，emptyList 按 productLine 分组求平均
4. **用户友好的排序**：GetPlanMetric 的 emptyList 按 value 降序排列
5. **灵活的查询**：支持多种聚合场景
6. **完善的错误处理**：提供详细的错误信息
7. **高效的数据库查询**：使用范围查询和条件过滤
8. **统一的时区处理**：与其他接口保持一致的 +08:00 时区
