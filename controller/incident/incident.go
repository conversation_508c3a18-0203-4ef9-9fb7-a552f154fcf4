package incident

import (
	"context"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent"
	"stabilityDigitalBase/library/ent/incident"
	"stabilityDigitalBase/library/errs"
	"stabilityDigitalBase/library/mysql"
)

const (
	// DefaultTimeout 默认超时时间
	DefaultTimeout = 30 * time.Second
)

// getDBClientWithContext 获取数据库客户端和上下文
func getDBClientWithContext() (*mysql.Client, context.Context, context.CancelFunc, error) {
	client, err := mysql.Database()
	if err != nil {
		return nil, nil, nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), DefaultTimeout)
	return client, ctx, cancel, nil
}

// CreateRequest 创建故障请求
type CreateRequest struct {
	HappenedAt  string `json:"happenedAt" binding:"required"`  // 发生时间
	Nature      string `json:"nature" binding:"required"`      // 故障性质：good/bad
	Summary     string `json:"summary" binding:"required"`     // 故障简述
	DirectCause string `json:"directCause" binding:"required"` // 直接原因
	Level       string `json:"level" binding:"required"`       // 故障级别：P0/P1/P2/P3/NONE
	Owner       string `json:"owner" binding:"required"`       // 故障负责人
}

// Create 一句话故障创建
func Create(c *gin.Context) {
	var req CreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 解析时间
	happenedAt, err := time.Parse("2006-01-02", req.HappenedAt)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("时间格式错误，请使用YYYY-MM-DD格式"))
		return
	}

	// 验证nature字段
	if req.Nature != "good" && req.Nature != "bad" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("nature字段只能是good或bad"))
		return
	}

	// 验证level字段
	validLevels := []string{"P0", "P1", "P2", "P3", "NONE"}
	isValidLevel := false
	for _, level := range validLevels {
		if req.Level == level {
			isValidLevel = true
			break
		}
	}
	if !isValidLevel {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("level字段只能是P0/P1/P2/P3/NONE"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 创建故障记录
	incidentRecord, err := client.Incident.Create().
		SetHappenedAt(happenedAt).
		SetNature(incident.Nature(req.Nature)).
		SetSummary(req.Summary).
		SetDirectCause(req.DirectCause).
		SetLevel(incident.Level(req.Level)).
		SetOwner(req.Owner).
		SetStatus(incident.StatusNormal). // 默认状态为normal
		Save(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 返回创建的故障ID
	gintool.JSON2FE(c, gin.H{"id": incidentRecord.ID}, nil)
}

// ModifyRequest 故障信息维护请求
type ModifyRequest struct {
	ID                     int64    `json:"id" binding:"required"`
	HappenedAt             string   `json:"happenedAt"`
	Nature                 string   `json:"nature"`
	Summary                string   `json:"summary"`
	Owner                  string   `json:"owner"`
	Level                  string   `json:"level"`
	DirectCause            string   `json:"directCause"`
	RootCause              string   `json:"rootCause"`
	Impact                 string   `json:"impact"`
	TimeDiscovered         *int     `json:"timeDiscovered"`         // 发现时长
	TimeLocated            *int     `json:"timeLocated"`            // 定位时长
	TimeRecovered          *int     `json:"timeRecovered"`          // 恢复时长
	AutoStopLoss           *bool    `json:"autoStopLoss"`           // 是否具备自动止损能力
	AutoStopLossEffectived *bool    `json:"autoStopLossEffectived"` // 自动止损是否生效
	Tag                    []string `json:"tag"`                    // 标签
}

// Modify 故障信息维护
func Modify(c *gin.Context) {
	var req ModifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 检查故障是否存在
	exists, err := client.Incident.Query().Where(incident.IDEQ(req.ID)).Exist(ctx)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	if !exists {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("故障记录不存在"))
		return
	}

	// 构建更新查询
	updateQuery := client.Incident.UpdateOneID(req.ID)

	// 更新基本信息
	if req.HappenedAt != "" {
		happenedAt, err := time.Parse("2006-01-02", req.HappenedAt)
		if err != nil {
			gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("时间格式错误"))
			return
		}
		updateQuery = updateQuery.SetHappenedAt(happenedAt)
	}

	if req.Nature != "" {
		updateQuery = updateQuery.SetNature(incident.Nature(req.Nature))
	}

	if req.Summary != "" {
		updateQuery = updateQuery.SetSummary(req.Summary)
	}

	if req.Owner != "" {
		updateQuery = updateQuery.SetOwner(req.Owner)
	}

	if req.Level != "" {
		updateQuery = updateQuery.SetLevel(incident.Level(req.Level))
	}

	if req.DirectCause != "" {
		updateQuery = updateQuery.SetDirectCause(req.DirectCause)
	}

	if req.RootCause != "" {
		updateQuery = updateQuery.SetNillableRootCause(&req.RootCause)
	}

	if req.Impact != "" {
		updateQuery = updateQuery.SetNillableImpact(&req.Impact)
	}

	if req.AutoStopLoss != nil {
		updateQuery = updateQuery.SetAutoStopLoss(*req.AutoStopLoss)
	}

	if req.AutoStopLossEffectived != nil {
		updateQuery = updateQuery.SetNillableAutoStopLossEffectived(req.AutoStopLossEffectived)
	}

	if req.TimeDiscovered != nil {
		updateQuery = updateQuery.SetNillableTimeDiscovered(req.TimeDiscovered)
	}

	if req.TimeLocated != nil {
		updateQuery = updateQuery.SetNillableTimeLocated(req.TimeLocated)
	}

	if req.TimeRecovered != nil {
		updateQuery = updateQuery.SetNillableTimeRecovered(req.TimeRecovered)
	}

	if req.Tag != nil {
		updateQuery = updateQuery.SetTag(req.Tag)
	}

	// 执行更新
	_, err = updateQuery.Save(ctx)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// UpdateStatusRequest 更新状态请求
type UpdateStatusRequest struct {
	ID     int64  `json:"id" binding:"required"`
	Status string `json:"status" binding:"required"`
}

// UpdateStatus 更新故障状态
func UpdateStatus(c *gin.Context) {
	var req UpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 更新状态
	_, err = client.Incident.UpdateOneID(req.ID).
		SetStatus(incident.Status(req.Status)).
		Save(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// ListRequest 列表查询请求
type ListRequest struct {
	Page     int    `form:"page"`
	PageSize int    `form:"page_size"`
	ID       *int64 `form:"id"`
	Nature   string `form:"nature"`
	Owner    string `form:"owner"`
	Level    string `form:"level"`
	Status   string `form:"status"`
}

// List 故障列表查询
func List(c *gin.Context) {
	var req ListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 构建查询条件
	query := client.Incident.Query().Where(incident.StatusNEQ(incident.StatusDeleted))

	if req.ID != nil {
		query = query.Where(incident.IDEQ(*req.ID))
	}
	if req.Nature != "" {
		query = query.Where(incident.NatureEQ(incident.Nature(req.Nature)))
	}
	if req.Owner != "" {
		query = query.Where(incident.OwnerContains(req.Owner))
	}
	if req.Level != "" {
		query = query.Where(incident.LevelEQ(incident.Level(req.Level)))
	}
	if req.Status != "" {
		query = query.Where(incident.StatusEQ(incident.Status(req.Status)))
	}

	// 获取总数
	total, err := query.Count(ctx)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 分页查询
	incidents, err := query.
		Offset((req.Page - 1) * req.PageSize).
		Limit(req.PageSize).
		Order(ent.Desc(incident.FieldCreatedAt)).
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建响应数据
	var result []map[string]interface{}
	for _, inc := range incidents {
		item := map[string]interface{}{
			"id":                     inc.ID,
			"happenedAt":             inc.HappenedAt.Format("2006-01-02"),
			"nature":                 inc.Nature,
			"summary":                inc.Summary,
			"owner":                  inc.Owner,
			"level":                  inc.Level,
			"directCause":            inc.DirectCause,
			"rootCause":              inc.RootCause,
			"impact":                 inc.Impact,
			"timeDiscovered":         inc.TimeDiscovered,
			"timeLocated":            inc.TimeLocated,
			"timeRecovered":          inc.TimeRecovered,
			"autoStopLoss":           inc.AutoStopLoss,
			"autoStopLossEffectived": inc.AutoStopLossEffectived,
			"tag":                    inc.Tag,
			"status":                 inc.Status,
			"createdAt":              inc.CreatedAt.Format("2006-01-02 15:04:05"),
			"updatedAt":              inc.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		result = append(result, item)
	}

	// 返回分页数据
	response := map[string]interface{}{
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
		"data":     result,
	}

	gintool.JSON2FE(c, response, nil)
}

// Detail 故障详情查询
func Detail(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("故障ID不能为空"))
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("故障ID格式错误"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询故障详情，包含关联数据
	inc, err := client.Incident.Query().
		Where(incident.IDEQ(id)).
		WithTimelines().
		WithProblems(func(q *ent.ProblemQuery) {
			q.WithImprovements()
		}).
		WithAttachments().
		Only(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("故障记录不存在"))
			return
		}
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建时间线数据
	var timelines []map[string]interface{}
	for _, timeline := range inc.Edges.Timelines {
		item := map[string]interface{}{
			"id":          timeline.ID,
			"startTime":   timeline.StartTime.Format("2006-01-02 15:04:05"),
			"endTime":     timeline.EndTime.Format("2006-01-02 15:04:05"),
			"type":        timeline.Type,
			"description": timeline.Description,
		}
		timelines = append(timelines, item)
	}

	// 构建问题和改进项数据
	var problems []map[string]interface{}
	for _, problem := range inc.Edges.Problems {
		// 构建该问题的改进项
		var improvements []map[string]interface{}
		for _, improvement := range problem.Edges.Improvements {
			improvementItem := map[string]interface{}{
				"id":           improvement.ID,
				"content":      improvement.Content,
				"owner":        improvement.Owner,
				"expectedTime": improvement.ExpectedTime.Format("2006-01-02"),
				"status":       improvement.Status,
			}
			improvements = append(improvements, improvementItem)
		}

		problemItem := map[string]interface{}{
			"id":           problem.ID,
			"phase":        problem.Phase,
			"content":      problem.Content,
			"improvements": improvements,
		}
		problems = append(problems, problemItem)
	}

	// 构建附件数据
	var attachments []int64
	for _, attachment := range inc.Edges.Attachments {
		attachments = append(attachments, attachment.ID)
	}

	// 构建响应数据
	result := map[string]interface{}{
		"id":                     inc.ID,
		"happenedAt":             inc.HappenedAt.Format("2006-01-02"),
		"nature":                 inc.Nature,
		"summary":                inc.Summary,
		"owner":                  inc.Owner,
		"level":                  inc.Level,
		"directCause":            inc.DirectCause,
		"rootCause":              inc.RootCause,
		"impact":                 inc.Impact,
		"timeDiscovered":         inc.TimeDiscovered,
		"timeLocated":            inc.TimeLocated,
		"timeRecovered":          inc.TimeRecovered,
		"autoStopLoss":           inc.AutoStopLoss,
		"autoStopLossEffectived": inc.AutoStopLossEffectived,
		"tag":                    inc.Tag,
		"timelines":              timelines,
		"problems":               problems,
		"attachments":            attachments,
		"status":                 inc.Status,
	}

	gintool.JSON2FE(c, result, nil)
}
