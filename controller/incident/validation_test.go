package incident

import (
	"testing"
	"time"
)

// TestCreateRequest_FieldValidation 测试CreateRequest结构体的字段验证
func TestCreateRequest_FieldValidation(t *testing.T) {
	tests := []struct {
		name    string
		req     CreateRequest
		wantErr bool
	}{
		{
			name: "有效的请求",
			req: CreateRequest{
				HappenedAt: "2025-08-21",
				Nature:     "bad",
				Summary:    "测试故障",
			},
			wantErr: false,
		},
		{
			name: "空的HappenedAt",
			req: CreateRequest{
				HappenedAt: "",
				Nature:     "bad",
				Summary:    "测试故障",
			},
			wantErr: true,
		},
		{
			name: "空的Nature",
			req: CreateRequest{
				HappenedAt: "2025-08-21",
				Nature:     "",
				Summary:    "测试故障",
			},
			wantErr: true,
		},
		{
			name: "空的Summary",
			req: CreateRequest{
				HappenedAt: "2025-08-21",
				Nature:     "bad",
				Summary:    "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试时间解析
			if tt.req.HappenedAt != "" {
				_, err := time.Parse("2006-01-02", tt.req.HappenedAt)
				if err != nil && !tt.wantErr {
					t.Errorf("时间解析失败: %v", err)
				}
			}

			// 测试nature字段验证
			if tt.req.Nature != "" && tt.req.Nature != "good" && tt.req.Nature != "bad" {
				if !tt.wantErr {
					t.Errorf("无效的nature值: %s", tt.req.Nature)
				}
			}

			// 测试必填字段
			hasEmptyRequired := tt.req.HappenedAt == "" || tt.req.Nature == "" || tt.req.Summary == ""
			if hasEmptyRequired != tt.wantErr {
				t.Errorf("必填字段验证失败，期望错误: %v, 实际有空字段: %v", tt.wantErr, hasEmptyRequired)
			}
		})
	}
}

// TestModifyRequest_FieldValidation 测试ModifyRequest结构体的字段验证
func TestModifyRequest_FieldValidation(t *testing.T) {
	tests := []struct {
		name    string
		req     ModifyRequest
		wantErr bool
	}{
		{
			name: "有效的请求",
			req: ModifyRequest{
				ID:    1,
				Owner: "测试负责人",
			},
			wantErr: false,
		},
		{
			name: "缺少ID",
			req: ModifyRequest{
				Owner: "测试负责人",
			},
			wantErr: true,
		},
		{
			name: "部分字段更新",
			req: ModifyRequest{
				ID:        1,
				RootCause: "根本原因",
				Impact:    "影响描述",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试ID字段
			hasValidID := tt.req.ID > 0
			if !hasValidID && !tt.wantErr {
				t.Errorf("ID字段验证失败，期望错误: %v, 实际ID: %d", tt.wantErr, tt.req.ID)
			}

			// 测试时间字段（如果提供）
			if tt.req.HappenedAt != "" {
				_, err := time.Parse("2006-01-02", tt.req.HappenedAt)
				if err != nil {
					t.Errorf("时间解析失败: %v", err)
				}
			}
		})
	}
}

// TestUpdateStatusRequest_FieldValidation 测试UpdateStatusRequest结构体的字段验证
func TestUpdateStatusRequest_FieldValidation(t *testing.T) {
	tests := []struct {
		name    string
		req     UpdateStatusRequest
		wantErr bool
	}{
		{
			name: "有效的请求",
			req: UpdateStatusRequest{
				ID:     1,
				Status: "resolved",
			},
			wantErr: false,
		},
		{
			name: "缺少ID",
			req: UpdateStatusRequest{
				Status: "resolved",
			},
			wantErr: true,
		},
		{
			name: "缺少Status",
			req: UpdateStatusRequest{
				ID: 1,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 测试必填字段
			hasValidID := tt.req.ID > 0
			hasValidStatus := tt.req.Status != ""

			isValid := hasValidID && hasValidStatus
			if isValid == tt.wantErr {
				t.Errorf("字段验证失败，期望错误: %v, 实际有效: %v", tt.wantErr, isValid)
			}
		})
	}
}

// TestListRequest_DefaultValues 测试ListRequest的默认值处理
func TestListRequest_DefaultValues(t *testing.T) {
	tests := []struct {
		name     string
		req      ListRequest
		wantPage int
		wantSize int
	}{
		{
			name:     "默认分页参数",
			req:      ListRequest{},
			wantPage: 1,
			wantSize: 20,
		},
		{
			name: "指定分页参数",
			req: ListRequest{
				Page:     2,
				PageSize: 10,
			},
			wantPage: 2,
			wantSize: 10,
		},
		{
			name: "页大小超过限制",
			req: ListRequest{
				Page:     1,
				PageSize: 200,
			},
			wantPage: 1,
			wantSize: 100, // 应该被限制为100
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟默认值处理逻辑
			page := tt.req.Page
			pageSize := tt.req.PageSize

			if page <= 0 {
				page = 1
			}
			if pageSize <= 0 {
				pageSize = 20
			}
			if pageSize > 100 {
				pageSize = 100
			}

			if page != tt.wantPage {
				t.Errorf("页码处理错误，期望: %d, 实际: %d", tt.wantPage, page)
			}
			if pageSize != tt.wantSize {
				t.Errorf("页大小处理错误，期望: %d, 实际: %d", tt.wantSize, pageSize)
			}
		})
	}
}
