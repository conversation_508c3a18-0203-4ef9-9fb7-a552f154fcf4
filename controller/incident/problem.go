package incident

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent"
	"stabilityDigitalBase/library/ent/problem"
	"stabilityDigitalBase/library/errs"
)

// CreateProblemRequest 创建问题请求
type CreateProblemRequest struct {
	IncidentID int64  `json:"incidentId" binding:"required"` // 故障ID
	Phase      string `json:"phase" binding:"required"`      // 阶段：deploy/monitor/operation
	Content    string `json:"content" binding:"required"`    // 问题内容
}

// CreateProblem 创建问题
func CreateProblem(c *gin.Context) {
	var req CreateProblemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 验证phase字段
	validPhases := []string{"deploy", "monitor", "operation"}
	isValidPhase := false
	for _, phase := range validPhases {
		if req.Phase == phase {
			isValidPhase = true
			break
		}
	}
	if !isValidPhase {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("phase字段只能是deploy/monitor/operation"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 创建问题记录
	problemRecord, err := client.Problem.Create().
		SetIncidentID(req.IncidentID).
		SetPhase(req.Phase).
		SetContent(req.Content).
		Save(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 返回创建的问题ID
	gintool.JSON2FE(c, gin.H{"id": problemRecord.ID}, nil)
}

// ModifyProblemRequest 修改问题请求
type ModifyProblemRequest struct {
	ID      int64  `json:"id" binding:"required"`
	Phase   string `json:"phase"`
	Content string `json:"content"`
}

// ModifyProblem 修改问题
func ModifyProblem(c *gin.Context) {
	var req ModifyProblemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 检查问题是否存在
	exists, err := client.Problem.Query().Where(problem.IDEQ(req.ID)).Exist(ctx)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	if !exists {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("问题记录不存在"))
		return
	}

	// 构建更新查询
	updateQuery := client.Problem.UpdateOneID(req.ID)

	if req.Phase != "" {
		// 验证phase字段
		validPhases := []string{"deploy", "monitor", "operation"}
		isValidPhase := false
		for _, phase := range validPhases {
			if req.Phase == phase {
				isValidPhase = true
				break
			}
		}
		if !isValidPhase {
			gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("phase字段只能是deploy/monitor/operation"))
			return
		}
		updateQuery = updateQuery.SetPhase(req.Phase)
	}

	if req.Content != "" {
		updateQuery = updateQuery.SetContent(req.Content)
	}

	// 执行更新
	_, err = updateQuery.Save(ctx)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// DeleteProblem 删除问题
func DeleteProblem(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("问题ID不能为空"))
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("问题ID格式错误"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 删除问题记录
	err = client.Problem.DeleteOneID(id).Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("问题记录不存在"))
			return
		}
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// ListProblems 查询故障的问题列表
func ListProblems(c *gin.Context) {
	incidentIDStr := c.Query("incidentId")
	if incidentIDStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("故障ID不能为空"))
		return
	}

	incidentID, err := strconv.ParseInt(incidentIDStr, 10, 64)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("故障ID格式错误"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询问题列表
	problems, err := client.Problem.Query().
		Where(problem.IncidentIDEQ(incidentID)).
		WithImprovements().
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建响应数据
	var result []map[string]interface{}
	for _, prob := range problems {
		// 构建改进项数据
		var improvements []map[string]interface{}
		for _, improvement := range prob.Edges.Improvements {
			improvementItem := map[string]interface{}{
				"id":           improvement.ID,
				"content":      improvement.Content,
				"owner":        improvement.Owner,
				"expectedTime": improvement.ExpectedTime.Format("2006-01-02"),
				"status":       improvement.Status,
			}
			improvements = append(improvements, improvementItem)
		}

		item := map[string]interface{}{
			"id":           prob.ID,
			"incidentId":   prob.IncidentID,
			"phase":        prob.Phase,
			"content":      prob.Content,
			"improvements": improvements,
			"createdAt":    prob.CreatedAt.Format("2006-01-02 15:04:05"),
			"updatedAt":    prob.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		result = append(result, item)
	}

	gintool.JSON2FE(c, result, nil)
}
