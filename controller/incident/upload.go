package incident

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/errs"
)

// UploadFileRequest 上传文件请求
type UploadFileRequest struct {
	IncidentID int64  `form:"incident_id" binding:"required"`
	Uploader   string `form:"uploader" binding:"required"`
}

// UploadFile 附件上传
func UploadFile(c *gin.Context) {
	var req UploadFileRequest
	if err := c.ShouldBind(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("文件上传失败"))
		return
	}
	defer file.Close()

	// TODO: 实现文件上传到FTP服务器的逻辑
	// 这里暂时返回一个模拟的附件ID
	attachmentID := time.Now().Unix()

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 创建附件记录
	_, err = client.Attachment.Create().
		SetIncidentID(req.IncidentID).
		SetType("document").
		SetFileName(header.Filename).
		SetFileSize(strconv.FormatInt(header.Size, 10)).
		SetMimeType(header.Header.Get("Content-Type")).
		SetUploader(req.Uploader).
		Save(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, gin.H{"id": attachmentID}, nil)
}
