package incident

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent"
	"stabilityDigitalBase/library/ent/improvement"
	"stabilityDigitalBase/library/errs"
)

// CreateImprovementRequest 创建改进项请求
type CreateImprovementRequest struct {
	ProblemID    int64  `json:"problemId" binding:"required"`    // 问题ID
	Content      string `json:"content" binding:"required"`      // 改进内容
	Owner        string `json:"owner" binding:"required"`        // 负责人
	ExpectedTime string `json:"expectedTime" binding:"required"` // 预期完成时间
	Status       string `json:"status" binding:"required"`       // 状态：pending/in_progress/completed/cancelled
}

// CreateImprovement 创建改进项
func CreateImprovement(c *gin.Context) {
	var req CreateImprovementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 解析预期完成时间
	expectedTime, err := time.Parse("2006-01-02", req.ExpectedTime)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("预期完成时间格式错误，请使用YYYY-MM-DD格式"))
		return
	}

	// 验证status字段
	validStatuses := []string{"pending", "in_progress", "completed", "cancelled"}
	isValidStatus := false
	for _, status := range validStatuses {
		if req.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("status字段只能是pending/in_progress/completed/cancelled"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 创建改进项记录
	improvementRecord, err := client.Improvement.Create().
		SetProblemID(req.ProblemID).
		SetContent(req.Content).
		SetOwner(req.Owner).
		SetExpectedTime(expectedTime).
		SetStatus(improvement.Status(req.Status)).
		Save(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 返回创建的改进项ID
	gintool.JSON2FE(c, gin.H{"id": improvementRecord.ID}, nil)
}

// ModifyImprovementRequest 修改改进项请求
type ModifyImprovementRequest struct {
	ID           int64  `json:"id" binding:"required"`
	Content      string `json:"content"`
	Owner        string `json:"owner"`
	ExpectedTime string `json:"expectedTime"`
	Status       string `json:"status"`
}

// ModifyImprovement 修改改进项
func ModifyImprovement(c *gin.Context) {
	var req ModifyImprovementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 检查改进项是否存在
	exists, err := client.Improvement.Query().Where(improvement.IDEQ(req.ID)).Exist(ctx)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	if !exists {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("改进项记录不存在"))
		return
	}

	// 构建更新查询
	updateQuery := client.Improvement.UpdateOneID(req.ID)

	if req.Content != "" {
		updateQuery = updateQuery.SetContent(req.Content)
	}

	if req.Owner != "" {
		updateQuery = updateQuery.SetOwner(req.Owner)
	}

	if req.ExpectedTime != "" {
		expectedTime, err := time.Parse("2006-01-02", req.ExpectedTime)
		if err != nil {
			gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("预期完成时间格式错误"))
			return
		}
		updateQuery = updateQuery.SetExpectedTime(expectedTime)
	}

	if req.Status != "" {
		// 验证status字段
		validStatuses := []string{"pending", "in_progress", "completed", "cancelled"}
		isValidStatus := false
		for _, status := range validStatuses {
			if req.Status == status {
				isValidStatus = true
				break
			}
		}
		if !isValidStatus {
			gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("status字段只能是pending/in_progress/completed/cancelled"))
			return
		}
		updateQuery = updateQuery.SetStatus(improvement.Status(req.Status))
	}

	// 执行更新
	_, err = updateQuery.Save(ctx)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// DeleteImprovement 删除改进项
func DeleteImprovement(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("改进项ID不能为空"))
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("改进项ID格式错误"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 删除改进项记录
	err = client.Improvement.DeleteOneID(id).Exec(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("改进项记录不存在"))
			return
		}
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// ListImprovements 查询问题的改进项列表
func ListImprovements(c *gin.Context) {
	problemIDStr := c.Query("problemId")
	if problemIDStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("问题ID不能为空"))
		return
	}

	problemID, err := strconv.ParseInt(problemIDStr, 10, 64)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("问题ID格式错误"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 查询改进项列表
	improvements, err := client.Improvement.Query().
		Where(improvement.ProblemIDEQ(problemID)).
		All(ctx)

	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建响应数据
	var result []map[string]interface{}
	for _, imp := range improvements {
		item := map[string]interface{}{
			"id":           imp.ID,
			"problemId":    imp.ProblemID,
			"content":      imp.Content,
			"owner":        imp.Owner,
			"expectedTime": imp.ExpectedTime.Format("2006-01-02"),
			"status":       imp.Status,
			"createdAt":    imp.CreatedAt.Format("2006-01-02 15:04:05"),
			"updatedAt":    imp.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		result = append(result, item)
	}

	gintool.JSON2FE(c, result, nil)
}

// UpdateImprovementStatusRequest 更新改进项状态请求
type UpdateImprovementStatusRequest struct {
	ID     int64  `json:"id" binding:"required"`
	Status string `json:"status" binding:"required"`
}

// UpdateImprovementStatus 更新改进项状态
func UpdateImprovementStatus(c *gin.Context) {
	var req UpdateImprovementStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 验证status字段
	validStatuses := []string{"pending", "in_progress", "completed", "cancelled"}
	isValidStatus := false
	for _, status := range validStatuses {
		if req.Status == status {
			isValidStatus = true
			break
		}
	}
	if !isValidStatus {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("status字段只能是pending/in_progress/completed/cancelled"))
		return
	}

	// 获取数据库客户端和上下文
	client, ctx, cancel, err := getDBClientWithContext()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	defer cancel()

	// 更新状态
	_, err = client.Improvement.UpdateOneID(req.ID).
		SetStatus(improvement.Status(req.Status)).
		Save(ctx)

	if err != nil {
		if ent.IsNotFound(err) {
			gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("改进项记录不存在"))
			return
		}
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}
