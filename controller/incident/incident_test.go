package incident

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// setupTestRouter 设置测试路由
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()

	// 注册路由
	incidentGroup := r.Group("/incident")
	{
		incidentGroup.POST("/create", Create)
		incidentGroup.POST("/modify", Modify)
		incidentGroup.POST("/updateStatus", UpdateStatus)
		incidentGroup.GET("/list", List)
		incidentGroup.GET("/detail/:id", Detail)
	}

	return r
}

// TestCreateRequest_Validation 测试创建请求的参数验证
func TestCreateRequest_Validation(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  bool
	}{
		{
			name: "有效的创建请求",
			requestBody: map[string]interface{}{
				"happenedAt": "2025-08-21",
				"nature":     "bad",
				"summary":    "测试故障",
			},
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
		{
			name: "缺少必填字段happenedAt",
			requestBody: map[string]interface{}{
				"nature":  "bad",
				"summary": "测试故障",
			},
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name: "缺少必填字段nature",
			requestBody: map[string]interface{}{
				"happenedAt": "2025-08-21",
				"summary":    "测试故障",
			},
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name: "缺少必填字段summary",
			requestBody: map[string]interface{}{
				"happenedAt": "2025-08-21",
				"nature":     "bad",
			},
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name: "无效的nature值",
			requestBody: map[string]interface{}{
				"happenedAt": "2025-08-21",
				"nature":     "invalid",
				"summary":    "测试故障",
			},
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name: "无效的日期格式",
			requestBody: map[string]interface{}{
				"happenedAt": "2025/08/21",
				"nature":     "bad",
				"summary":    "测试故障",
			},
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备请求体
			jsonBody, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/incident/create", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")

			// 执行请求
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证状态码
			assert.Equal(t, tt.expectedStatus, w.Code)

			// 解析响应
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			// 验证是否有错误
			if tt.expectedError {
				assert.NotEqual(t, float64(0), response["code"])
			} else {
				// 注意：由于没有真实的数据库连接，这里会返回数据库错误
				// 在实际测试中，应该使用mock数据库或测试数据库
				// 这里我们只验证参数验证部分是否正确
			}
		})
	}
}

// TestListRequest_Pagination 测试列表请求的分页参数
func TestListRequest_Pagination(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "默认分页参数",
			queryParams:    "",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "指定页码和页大小",
			queryParams:    "?page=2&page_size=10",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "页大小超过限制",
			queryParams:    "?page=1&page_size=200",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "带筛选条件",
			queryParams:    "?nature=bad&owner=测试&level=P1",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", "/incident/list"+tt.queryParams, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			// 验证响应格式
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
		})
	}
}

// TestModifyRequest_Validation 测试修改请求的参数验证
func TestModifyRequest_Validation(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  bool
	}{
		{
			name: "有效的修改请求",
			requestBody: map[string]interface{}{
				"id":    1,
				"owner": "新负责人",
				"level": "P1",
			},
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
		{
			name: "缺少必填字段id",
			requestBody: map[string]interface{}{
				"owner": "新负责人",
			},
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name: "部分字段更新",
			requestBody: map[string]interface{}{
				"id":        1,
				"rootCause": "根本原因分析",
				"impact":    "影响范围描述",
			},
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBody, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/incident/modify", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			if tt.expectedError {
				assert.NotEqual(t, float64(0), response["code"])
			}
		})
	}
}

// TestUpdateStatusRequest_Validation 测试状态更新请求的参数验证
func TestUpdateStatusRequest_Validation(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		requestBody    map[string]interface{}
		expectedStatus int
		expectedError  bool
	}{
		{
			name: "有效的状态更新请求",
			requestBody: map[string]interface{}{
				"id":     1,
				"status": "resolved",
			},
			expectedStatus: http.StatusOK,
			expectedError:  false,
		},
		{
			name: "缺少必填字段id",
			requestBody: map[string]interface{}{
				"status": "resolved",
			},
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
		{
			name: "缺少必填字段status",
			requestBody: map[string]interface{}{
				"id": 1,
			},
			expectedStatus: http.StatusOK,
			expectedError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jsonBody, _ := json.Marshal(tt.requestBody)
			req, _ := http.NewRequest("POST", "/incident/updateStatus", bytes.NewBuffer(jsonBody))
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			if tt.expectedError {
				assert.NotEqual(t, float64(0), response["code"])
			}
		})
	}
}

// TestDetailRequest_PathParam 测试详情请求的路径参数
func TestDetailRequest_PathParam(t *testing.T) {
	router := setupTestRouter()

	tests := []struct {
		name           string
		path           string
		expectedStatus int
	}{
		{
			name:           "有效的ID",
			path:           "/incident/detail/123",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "无效的ID格式",
			path:           "/incident/detail/abc",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", tt.path, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
		})
	}
}
