package dashboard

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent"
	"stabilityDigitalBase/library/ent/metric"
	"stabilityDigitalBase/library/errs"
	"stabilityDigitalBase/library/mysql"
)

// OperationMetric 操作风险类指标结构
type OperationMetric struct {
	Total       int `json:"total"`
	NotReported int `json:"notReported"`
	NotChecked  int `json:"notChecked"`
	NotStandard int `json:"notStandard"`
}

// CapacityMetric 容量类指标结构
type CapacityMetric struct {
	Coverage     float64 `json:"coverage"`
	SuccessRate  float64 `json:"successRate"`
	Efficiency   float64 `json:"efficiency"`
	AverageHours float64 `json:"averageHours"`
}

// DeploymentMetric 部署类指标结构
type RollbackItem struct {
	Name  string  `json:"name"`
	Value float64 `json:"value"`
}
type DeploymentMetric struct {
	FailureRate  float64         `json:"failureRate"`
	RollbackRate float64         `json:"rollbackRate"`
	RollbackList []*RollbackItem `json:"rollbackList"`
}

// MonitorMetric 监控类指标结构
type NotRecalledInfo struct {
	P0 int `json:"p0"`
	P1 int `json:"p1"`
}
type MonitorMetric struct {
	MachineCoverage             float64          `json:"machineCoverage"`
	BnsCoverage                 float64          `json:"bnsCoverage"`
	CrossDepartmentBreachedRate float64          `json:"crossDepartmentBreachedRate"`
	CrossDepartmentTotal        int              `json:"crossDepartmentTotal"`
	CrossDepartmentBuilt        int              `json:"crossDepartmentBuilt"`
	Recalled                    int              `json:"recalled"`
	NotRecalled                 *NotRecalledInfo `json:"notRecalled"`
}

// EmptyItem 空预案项结构
type EmptyItem struct {
	ProductLine string  `json:"productLine"`
	Value       float64 `json:"value"`
}

// PlanMetric 预案平台类指标结构
type PlanMetric struct {
	Coverage    float64      `json:"coverage"`
	FailureRate float64      `json:"failureRate"`
	EmptyRate   float64      `json:"emptyRate"`
	EmptyList   []*EmptyItem `json:"emptyList"`
}

type Metrics struct {
	Team       string            `json:"team"`
	Business   string            `json:"business"`
	Month      string            `json:"month"`
	Operation  *OperationMetric  `json:"operation"`
	Capacity   *CapacityMetric   `json:"capacity"`
	Deployment *DeploymentMetric `json:"deployment"`
	Monitor    *MonitorMetric    `json:"monitor"`
	Plan       *PlanMetric       `json:"plan"`
}

// 导入数据
func ImportMetrics(c *gin.Context) {
	var params Metrics
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 使用 Asia/Shanghai 时区解析时间
	loc, _ := time.LoadLocation("Asia/Shanghai")
	queryTime, err := time.ParseInLocation("2006-01", params.Month, loc)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(fmt.Sprintf("endMonth格式错误: %v", err)))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	operationValueStr, _ := json.Marshal(params.Operation)
	capacityValueStr, _ := json.Marshal(params.Capacity)
	deploymentValueStr, _ := json.Marshal(params.Deployment)
	monitorValueStr, _ := json.Marshal(params.Monitor)
	planValueStr, _ := json.Marshal(params.Plan)

	bulkCreates := []*ent.MetricCreate{
		db.Metric.Create().SetTeam(params.Team).SetBusiness(params.Business).SetMonth(queryTime).
			SetName("operation").SetAlias("操作风险类").SetValue(string(operationValueStr)),
		db.Metric.Create().SetTeam(params.Team).SetBusiness(params.Business).SetMonth(queryTime).
			SetName("capacity").SetAlias("容量类").SetValue(string(capacityValueStr)),
		db.Metric.Create().SetTeam(params.Team).SetBusiness(params.Business).SetMonth(queryTime).
			SetName("deployment").SetAlias("部署类").SetValue(string(deploymentValueStr)),
		db.Metric.Create().SetTeam(params.Team).SetBusiness(params.Business).SetMonth(queryTime).
			SetName("monitor").SetAlias("监控类").SetValue(string(monitorValueStr)),
		db.Metric.Create().SetTeam(params.Team).SetBusiness(params.Business).SetMonth(queryTime).
			SetName("plan").SetAlias("预案类").SetValue(string(planValueStr)),
	}

	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Metric.CreateBulk(bulkCreates...).Exec(ctx)
	cancel()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// 指标更新
func UpdateMetrics(c *gin.Context) {
	var params Metrics
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 使用 Asia/Shanghai 时区解析时间
	loc, _ := time.LoadLocation("Asia/Shanghai")
	queryTime, err := time.ParseInLocation("2006-01", params.Month, loc)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(fmt.Sprintf("endMonth格式错误: %v", err)))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	//
	g, _ := errgroup.WithContext(context.Background())
	if params.Operation != nil {
		g.Go(func() error {
			operationValueStr, _ := json.Marshal(params.Operation)
			ctx, cancel := mysql.ContextWithTimeout()
			err = db.Metric.Update().SetValue(string(operationValueStr)).Where(
				metric.TeamEQ(params.Team),
				metric.BusinessEQ(params.Business),
				metric.MonthEQ(queryTime),
				metric.NameEQ("operation"),
			).Exec(ctx)
			cancel()
			return err
		})
	}
	if params.Capacity != nil {
		g.Go(func() error {
			capacityValueStr, _ := json.Marshal(params.Capacity)
			ctx, cancel := mysql.ContextWithTimeout()
			err = db.Metric.Update().SetValue(string(capacityValueStr)).Where(
				metric.TeamEQ(params.Team),
				metric.BusinessEQ(params.Business),
				metric.MonthEQ(queryTime),
				metric.NameEQ("capacity"),
			).Exec(ctx)
			cancel()
			return err
		})
	}
	if params.Deployment != nil {
		g.Go(func() error {
			deploymentValueStr, _ := json.Marshal(params.Deployment)
			ctx, cancel := mysql.ContextWithTimeout()
			err = db.Metric.Update().SetValue(string(deploymentValueStr)).Where(
				metric.TeamEQ(params.Team),
				metric.BusinessEQ(params.Business),
				metric.MonthEQ(queryTime),
				metric.NameEQ("deployment"),
			).Exec(ctx)
			cancel()
			return err
		})
	}
	if params.Monitor != nil {
		g.Go(func() error {
			monitorValueStr, _ := json.Marshal(params.Monitor)
			ctx, cancel := mysql.ContextWithTimeout()
			err = db.Metric.Update().SetValue(string(monitorValueStr)).Where(
				metric.TeamEQ(params.Team),
				metric.BusinessEQ(params.Business),
				metric.MonthEQ(queryTime),
				metric.NameEQ("monitor"),
			).Exec(ctx)
			cancel()
			return err
		})
	}
	if params.Plan != nil {
		g.Go(func() error {
			planValueStr, _ := json.Marshal(params.Plan)
			ctx, cancel := mysql.ContextWithTimeout()
			err = db.Metric.Update().SetValue(string(planValueStr)).Where(
				metric.TeamEQ(params.Team),
				metric.BusinessEQ(params.Business),
				metric.MonthEQ(queryTime),
				metric.NameEQ("plan"),
			).Exec(ctx)
			cancel()
			return err
		})
	}
	if g.Wait() != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}
