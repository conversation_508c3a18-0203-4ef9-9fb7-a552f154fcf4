package dashboard

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"stabilityDigitalBase/library/ent"
	"stabilityDigitalBase/library/ent/contact"
	"stabilityDigitalBase/library/errs"
	"stabilityDigitalBase/library/mysql"
)

// ContactInfo 接口人信息结构
type ContactInfo struct {
	Team      string `json:"team,omitempty"`
	Business  string `json:"business,omitempty"`
	Account   string `json:"account"`
	Name      string `json:"name"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}

type ContactResponse struct {
	Current *ContactInfo   `json:"current"`
	History []*ContactInfo `json:"history"`
}

// GetContact 获取接口人信息
func GetContact(c *gin.Context) {
	team := c.Query("team")
	business := c.Query("business")
	endMonth := c.Query("endMonth")
	if team == "" || business == "" || endMonth == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("team、business、startMonth、endMonth参数不能为空"))
		return
	}
	queryEnd, err := time.Parse("2006-01", endMonth)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(fmt.Sprintf("endMonth格式错误: %v", err)))
		return
	}

	// 获取数据库客户端和上下文
	db, err := mysql.Database()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 按startTime倒排查询接口人信息
	ctx, cancel := mysql.ContextWithTimeout()
	contacts, err := db.Contact.Query().
		Where(contact.TeamEQ(team), contact.BusinessEQ(business)).
		Order(contact.ByStartTime(sql.OrderDesc())).
		All(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 构建接口人信息列表，并根据时间范围筛选
	resBody := &ContactResponse{
		History: make([]*ContactInfo, len(contacts)),
	}
	for i, contact := range contacts {
		resBody.History[i] = &ContactInfo{
			Account:   contact.Account,
			Name:      contact.Name,
			StartTime: contact.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:   contact.EndTime.Format("2006-01-02 15:04:05"),
		}

		// 找到第一个非空的current，就不再变了
		if resBody.Current != nil {
			continue
		}

		// 如果接口人还在职（endTime为零值），则endTime设为当前时间
		if contact.EndTime.IsZero() {
			resBody.History[i].EndTime = ""
			contact.EndTime = time.Now()
		}

		// endMonth在startTime和endTime之间的，认为是current contact
		if queryEnd.After(contact.StartTime) && queryEnd.Before(contact.EndTime) {
			resBody.Current = resBody.History[i]
		}
	}

	gintool.JSON2FE(c, resBody, nil)
}

// 新建接口人
func CreateContact(c *gin.Context) {
	var params ContactInfo
	if err := c.ShouldBindJSON(&params); err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	loc, _ := time.LoadLocation("Asia/Shanghai")
	startTime, err := time.ParseInLocation("2006-01-02", params.StartTime, loc)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(fmt.Sprintf("startTime格式错误: %v", err)))
		return
	}

	// 获取数据库客户端和上下文
	db, err := mysql.Database()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Transaction(ctx, func(tx *ent.Tx) error {
		// step1.找到当前任职的contact
		currentOne, err := db.Contact.Query().Where(contact.TeamEQ(params.Team), contact.BusinessEQ(params.Business)).
			Order(contact.ByStartTime(sql.OrderDesc())).First(ctx)
		if ent.IsNotFound(err) {
			return err
		}
		// 判断任职时间，新接口人的任职日期不能小于当前任职人的任职日期
		if currentOne != nil && currentOne.StartTime.After(startTime) {
			return nil
		}

		// step2.创建新纪录
		_, err = db.Contact.Create().SetTeam(params.Team).SetBusiness(params.Business).
			SetAccount(params.Account).SetName(params.Name).SetStartTime(startTime).
			Save(ctx)
		if err != nil {
			return err
		}

		// step3.修改旧纪录的endTime
		if currentOne != nil {
			return db.Contact.Update().SetEndTime(time.Now()).Where(contact.IDEQ(currentOne.ID)).Exec(ctx)
		}
		return nil
	})
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}
