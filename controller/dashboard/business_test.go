package dashboard

import (
	"context"
	"encoding/json"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"

	"stabilityDigitalBase/env"
	"stabilityDigitalBase/library/mysql"
)

// TestGetBusiness 测试业务团队接口
func TestGetBusiness(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test: check args",
			before: func() {
				db, _ := mysql.Database()
				db.Contact.Create().SetTeam("信贷团队").SetBusiness("信贷稳定性接口人1").SetAccount("yangfengxin_dxm").SetName("杨凤心").SetStartTime(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)).SetEndTime(time.Date(2023, 12, 31, 0, 0, 0, 0, time.Local)).ExecX(context.Background())
				db.Contact.Create().SetTeam("信贷团队").SetBusiness("信贷稳定性接口人1").SetAccount("huangchengjuan_dxm").SetName("黄成娟").SetStartTime(time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)).SetEndTime(time.Date(2024, 12, 31, 0, 0, 0, 0, time.Local)).ExecX(context.Background())
				db.Contact.Create().SetTeam("信贷团队").SetBusiness("信贷稳定性接口人1").SetAccount("zhaopinglan_dxm").SetName("赵平兰").SetStartTime(time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local)).ExecX(context.Background())

				db.Contact.Create().SetTeam("信贷团队").SetBusiness("信贷稳定性接口人2").SetAccount("zhangsan_dxm").SetName("张三").SetStartTime(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)).SetEndTime(time.Date(2023, 12, 31, 0, 0, 0, 0, time.Local)).ExecX(context.Background())
				db.Contact.Create().SetTeam("信贷团队").SetBusiness("信贷稳定性接口人2").SetAccount("lisi_dxm").SetName("李四").SetStartTime(time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)).SetEndTime(time.Date(2024, 12, 31, 0, 0, 0, 0, time.Local)).ExecX(context.Background())
				db.Contact.Create().SetTeam("信贷团队").SetBusiness("信贷稳定性接口人2").SetAccount("wangwu_dxm").SetName("王五").SetStartTime(time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local)).ExecX(context.Background())

				db.Contact.Create().SetTeam("支付团队").SetBusiness("支付稳定性接口人1").SetAccount("zhaoliu_dxm").SetName("赵六").SetStartTime(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)).SetEndTime(time.Date(2023, 12, 31, 0, 0, 0, 0, time.Local)).ExecX(context.Background())
				db.Contact.Create().SetTeam("支付团队").SetBusiness("支付稳定性接口人1").SetAccount("guofugui_dxm").SetName("郭富贵").SetStartTime(time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)).SetEndTime(time.Date(2024, 12, 31, 0, 0, 0, 0, time.Local)).ExecX(context.Background())
				db.Contact.Create().SetTeam("支付团队").SetBusiness("支付稳定性接口人1").SetAccount("xuefacai_dxm").SetName("薛发财").SetStartTime(time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local)).ExecX(context.Background())
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]json.RawMessage
				json.Unmarshal(b, &resp)
				if string(resp["code"]) != "0" {
					t.Error(w.Code, string(b))
					return
				}

				result := []*TeamBusinesses{}
				json.Unmarshal(resp["data"], &result)
				if len(result) != 2 {
					t.Error("业务团队数量错误", len(result))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetBusiness(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
